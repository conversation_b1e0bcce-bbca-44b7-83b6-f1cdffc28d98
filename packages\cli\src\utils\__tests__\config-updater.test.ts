/**
 * Tests for ConfigUpdater with LLM integration using @openai/agents
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

import type { WidgetGenerationOptions } from "../../types.js";
import { ConfigUpdater } from "../config-updater.js";

// Mock the FileSystemManager
vi.mock("../filesystem-manager.js", () => ({
  FileSystemManager: vi.fn().mockImplementation(() => ({
    fileExists: vi.fn(),
    readFile: vi.fn(),
    writeFile: vi.fn(),
    updateFile: vi.fn(),
  })),
}));

// Mock the Logger
vi.mock("../logger.js", () => ({
  Logger: {
    info: vi.fn(),
    warning: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock @openai/agents
vi.mock("@openai/agents", () => ({
  Agent: vi.fn().mockImplementation(() => ({})),
  run: vi.fn(),
}));

describe("ConfigUpdater", () => {
  let configUpdater: ConfigUpdater;
  let mockFsManager: any;
  let mockRun: any;

  const sampleOptions: WidgetGenerationOptions = {
    name: "TestWidget",
    type: "basic",
    targetPath: "/test/path",
    description: "A test widget",
    placement: "message",
    slot: "blocks",
  };

  const sampleExistingConfig = `import type { AgentChatConfig } from "@cscs-agent/core";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "Test Agent",
      code: "test-agent",
      description: "A test agent",
      message: {
        blocks: {
          widgets: [],
        },
      },
      request: {
        chat: {
          url: "/api/chat",
          method: "POST",
        },
      },
    },
  ],
};`;

  beforeEach(async () => {
    // Import the mocked modules
    const { run } = await import("@openai/agents");
    mockRun = run as any;

    configUpdater = new ConfigUpdater();

    // Create mock methods
    mockFsManager = {
      fileExists: vi.fn(),
      readFile: vi.fn(),
      writeFile: vi.fn(),
      updateFile: vi.fn(),
    };

    // Replace the fsManager instance
    (configUpdater as any).fsManager = mockFsManager;

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("updateAgentConfig", () => {
    it("should create new config file when it does not exist", async () => {
      mockFsManager.fileExists.mockReturnValue(false);

      await configUpdater.updateAgentConfig(
        "/test/agent-config.tsx",
        sampleOptions,
        "./widgets/testwidget",
        "@Custom/TestWidget",
      );

      expect(mockFsManager.writeFile).toHaveBeenCalledWith(
        "/test/agent-config.tsx",
        expect.stringContaining('import TestWidget from "./widgets/testwidget"'),
      );
    });

    it("should use LLM to update existing config using @openai/agents", async () => {
      mockFsManager.fileExists.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue(sampleExistingConfig);

      // Mock successful LLM response from @openai/agents
      const mockLLMResponse = `import type { AgentChatConfig } from "@cscs-agent/core";
import TestWidget from "./widgets/testwidget";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "Test Agent",
      code: "test-agent",
      description: "A test agent",
      message: {
        blocks: {
          widgets: [
            {
              code: "@Custom/TestWidget",
              component: TestWidget,
            }
          ],
        },
      },
      request: {
        chat: {
          url: "/api/chat",
          method: "POST",
        },
      },
    },
  ],
};`;

      mockRun.mockResolvedValue({
        finalOutput: mockLLMResponse,
      });

      await configUpdater.updateAgentConfig(
        "/test/agent-config.tsx",
        sampleOptions,
        "./widgets/testwidget",
        "@Custom/TestWidget",
      );

      expect(mockRun).toHaveBeenCalledWith(
        expect.any(Object), // The agent instance
        expect.stringContaining("Update the existing agent configuration file"),
      );

      expect(mockFsManager.updateFile).toHaveBeenCalledWith(
        "/test/agent-config.tsx",
        expect.stringContaining('import TestWidget from "./widgets/testwidget"'),
        true,
      );
    });

    it("should throw error when LLM fails", async () => {
      mockFsManager.fileExists.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue(sampleExistingConfig);

      // Mock failed LLM response
      mockRun.mockRejectedValue(new Error("LLM API Error"));

      await expect(
        configUpdater.updateAgentConfig(
          "/test/agent-config.tsx",
          sampleOptions,
          "./widgets/testwidget",
          "@Custom/TestWidget",
        ),
      ).rejects.toThrow("LLM API Error");

      expect(mockRun).toHaveBeenCalled();
      expect(mockFsManager.updateFile).not.toHaveBeenCalled();
    });

    it("should throw error when LLM returns invalid response", async () => {
      mockFsManager.fileExists.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue(sampleExistingConfig);

      // Mock LLM response with no finalOutput
      mockRun.mockResolvedValue({
        finalOutput: null,
      });

      await expect(
        configUpdater.updateAgentConfig(
          "/test/agent-config.tsx",
          sampleOptions,
          "./widgets/testwidget",
          "@Custom/TestWidget",
        ),
      ).rejects.toThrow("LLM did not return valid configuration content");

      expect(mockRun).toHaveBeenCalled();
      expect(mockFsManager.updateFile).not.toHaveBeenCalled();
    });
  });

  describe("generateNewAgentConfig", () => {
    it("should generate correct config for message placement", () => {
      const result = (configUpdater as any).generateNewAgentConfig(
        sampleOptions,
        "./widgets/testwidget",
        "@Custom/TestWidget",
      );

      expect(result).toContain('import TestWidget from "./widgets/testwidget"');
      expect(result).toContain('code: "@Custom/TestWidget"');
      expect(result).toContain("component: TestWidget");
      expect(result).toContain("message: {");
      expect(result).toContain("blocks: {");
    });

    it("should generate correct config for sidePanel placement", () => {
      const sidePanelOptions = { ...sampleOptions, placement: "sidePanel" as const };

      const result = (configUpdater as any).generateNewAgentConfig(
        sidePanelOptions,
        "./widgets/testwidget",
        "@Custom/TestWidget",
      );

      expect(result).toContain("sidePanel: {");
      expect(result).toContain("render: {");
    });
  });
});
