/**
 * Agent configuration update utilities using LLM
 */

import { Agent, run } from "@openai/agents";

import type { WidgetGenerationOptions } from "../types.js";
import { FileSystemManager } from "./filesystem-manager.js";
import { Logger } from "./logger.js";

export class ConfigUpdater {
  private fsManager: FileSystemManager;
  private agent: Agent;

  constructor() {
    this.fsManager = new FileSystemManager();

    // Initialize the LLM agent for configuration updates
    this.agent = new Agent({
      name: "ConfigUpdater",
      instructions: `You are an expert TypeScript developer specializing in CSCS Agent configuration files.
        Your task is to read and modify agent configuration files to include new widget components.

        Always:
        - Maintain existing code structure, formatting, and style
        - Preserve all existing imports, configurations, and widgets
        - Add import statements for new components at the top with other imports
        - Place widgets in the correct sections based on placement and slot
        - Follow TypeScript best practices and ensure syntax correctness
        - Return ONLY the complete updated configuration file content without explanations`,
      model: process.env.OPENAI_MODEL,
    });
  }

  /**
   * Update agent configuration to include new widget using LLM
   */
  async updateAgentConfig(
    configPath: string,
    options: WidgetGenerationOptions,
    widgetImportPath: string,
    widgetCode: string,
  ): Promise<void> {
    if (!this.fsManager.fileExists(configPath)) {
      // Create new agent config file
      const newConfigContent = this.generateNewAgentConfig(options, widgetImportPath, widgetCode);
      await this.fsManager.writeFile(configPath, newConfigContent);
      Logger.info("Created new agent-config.tsx file");
    } else {
      // Update existing agent config file using LLM
      const existingContent = await this.fsManager.readFile(configPath);

      try {
        const updatedContent = await this.updateExistingAgentConfigWithLLM(
          existingContent,
          options,
          widgetImportPath,
          widgetCode,
        );
        await this.fsManager.updateFile(configPath, updatedContent, true);
        Logger.info("Updated agent-config.tsx file using LLM");
      } catch (error) {
        Logger.error(`LLM config update failed: ${error instanceof Error ? error.message : "Unknown error"}`);
        throw error;
      }
    }
  }

  /**
   * Generate new agent config content
   */
  private generateNewAgentConfig(options: WidgetGenerationOptions, importPath: string, widgetCode: string): string {
    const componentName = options.name;

    return `import type { AgentChatConfig } from "@cscs-agent/core";
import ${componentName} from "${importPath}";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "Generated Agent",
      code: "generated-agent",
      description: "Agent with generated widgets",
      ${this.generateWidgetPlacement(options, componentName, widgetCode)}
      request: {
        chat: {
          url: "/api/chat",
          method: "POST",
        },
      },
    },
  ],
};
`;
  }

  /**
   * Update existing agent config content using LLM
   */
  private async updateExistingAgentConfigWithLLM(
    content: string,
    options: WidgetGenerationOptions,
    importPath: string,
    widgetCode: string,
  ): Promise<string> {
    const prompt = this.buildConfigUpdatePrompt(content, options, importPath, widgetCode);

    const result = await run(this.agent, prompt);

    if (!result.finalOutput || typeof result.finalOutput !== "string") {
      throw new Error("LLM did not return valid configuration content");
    }

    return this.extractConfigFromResponse(result.finalOutput);
  }

  /**
   * Generate widget placement configuration
   */
  private generateWidgetPlacement(options: WidgetGenerationOptions, componentName: string, widgetCode: string): string {
    const widgetConfig = `{
          code: "${widgetCode}",
          component: ${componentName},
        }`;

    if (options.placement === "message") {
      if (options.slot === "blocks") {
        return `message: {
        blocks: {
          widgets: [
            ${widgetConfig}
          ],
        },
      },`;
      } else {
        return `message: {
        slots: {
          ${options.slot}: {
            widgets: [
              ${widgetConfig}
            ],
          },
        },
      },`;
      }
    } else if (options.placement === "sender") {
      return `sender: {
        slots: {
          ${options.slot}: {
            widgets: [
              ${widgetConfig}
            ],
          },
        },
      },`;
    } else if (options.placement === "sidePanel") {
      return `sidePanel: {
        render: {
          widgets: [
            ${widgetConfig}
          ],
        },
      },`;
    }

    return "";
  }

  /**
   * Build prompt for LLM to update configuration
   */
  private buildConfigUpdatePrompt(
    content: string,
    options: WidgetGenerationOptions,
    importPath: string,
    widgetCode: string,
  ): string {
    return `You are an expert TypeScript developer working with CSCS Agent configuration files.

**Task**: Update the existing agent configuration file to include a new widget component.

**Current Configuration File Content**:
\`\`\`typescript
${content}
\`\`\`

**Widget Details**:
- Component Name: ${options.name}
- Import Path: ${importPath}
- Widget Code: ${widgetCode}
- Placement: ${options.placement}
- Slot: ${options.slot || "blocks"}
- Description: ${options.description || "A custom widget component"}

**Requirements**:
1. Add the import statement for the new component at the top with other imports
2. Add the widget configuration to the appropriate section based on placement and slot:
   - For placement "message" and slot "blocks": add to message.blocks.widgets array
   - For placement "message" and other slots: add to message.slots.{slot}.widgets array
   - For placement "sender": add to sender.slots.{slot}.widgets array
   - For placement "sidePanel": add to sidePanel.render.widgets array
3. Maintain the existing structure and formatting
4. Preserve all existing imports, configurations, and widgets
5. Follow the existing code style and indentation
6. Ensure the TypeScript syntax is correct

**Widget Configuration Format**:
\`\`\`typescript
{
  code: "${widgetCode}",
  component: ${options.name},
}
\`\`\`

**Important**: Return ONLY the complete updated configuration file content. Do not include any explanations or markdown code blocks.`;
  }

  /**
   * Extract configuration content from LLM response
   */
  private extractConfigFromResponse(response: string): string {
    let config = response.trim();

    // Remove markdown code blocks if present
    config = config.replace(/^```(?:typescript|tsx|ts|javascript|jsx|js)?\n/, "");
    config = config.replace(/\n```$/, "");

    // Ensure the config contains required elements
    if (!config.includes("import") || !config.includes("export")) {
      throw new Error("Generated configuration appears to be incomplete");
    }

    return config;
  }
}
