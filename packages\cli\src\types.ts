/**
 * Type definitions for CSCS Agent CLI
 */

export interface TemplateMetadata {
  name: string;
  description: string;
  version: string;
  valid: boolean;
  path: string;
}

export interface ProjectOptions {
  name: string;
  template: string;
  directory?: string;
  skipInstall?: boolean;
  packageManager?: "npm" | "yarn" | "pnpm";
}

export interface CreateCommandOptions {
  template?: string;
  directory?: string;
  skipInstall?: boolean;
  packageManager?: string;
  interactive?: boolean;
}

export interface PackageJson {
  name: string;
  version: string;
  description?: string;
  private?: boolean;
  type?: string;
  scripts?: Record<string, string>;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  [key: string]: any;
}

export interface GenerateCommandOptions {
  name?: string;
  targetPath?: string;
  description?: string;
  interactive?: boolean;
  props?: string;
}

export interface WidgetGenerationOptions {
  name: string;
  type: string;
  targetPath: string;
  description?: string;
  props?: Record<string, any>;
  placement: "message" | "sender" | "sidePanel";
  slot?: "blocks" | "header" | "footer" | "headerPanel" | "render";
}

export interface ProjectStructure {
  hasAgentConfig: boolean;
  agentConfigPath: string;
  widgetsDir: string;
  srcDir: string;
  packageJsonPath: string;
}

export interface ValidationResult {
  valid: boolean;
  error?: string;
}

export interface CliConfig {
  templatesDir: string;
  defaultTemplate: string;
  supportedPackageManagers: string[];
}
